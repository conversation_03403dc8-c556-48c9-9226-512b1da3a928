const { db } = require('../db/database');

async function debugPlatformIcons() {
  console.log('🔍 Debugging platform icons...\n');

  try {
    // Get all streams
    const streams = await new Promise((resolve, reject) => {
      db.all('SELECT id, title, platform, platform_icon, rtmp_url FROM streams', (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });

    console.log(`📋 Found ${streams.length} streams in database:\n`);

    streams.forEach((stream, index) => {
      console.log(`${index + 1}. Stream: "${stream.title}"`);
      console.log(`   Platform: ${stream.platform}`);
      console.log(`   Platform Icon: ${stream.platform_icon}`);
      console.log(`   RTMP URL: ${stream.rtmp_url}`);
      console.log('');
    });

    // Test the API response
    console.log('🌐 Testing API response...\n');
    
    const Stream = require('../models/Stream');
    const apiStreams = await Stream.findAll('28fd3c08-af4b-4fe8-9b36-12e0089bd409'); // Use your user ID
    
    console.log('API Response:');
    apiStreams.forEach((stream, index) => {
      console.log(`${index + 1}. API Stream: "${stream.title}"`);
      console.log(`   Platform: ${stream.platform}`);
      console.log(`   Platform Icon: ${stream.platform_icon}`);
      console.log(`   Full stream object keys:`, Object.keys(stream));
      console.log('');
    });

  } catch (error) {
    console.error('❌ Error debugging platform icons:', error);
  }
}

debugPlatformIcons().then(() => {
  console.log('🔍 Platform icon debug completed!');
  process.exit(0);
}).catch(error => {
  console.error('❌ Debug script failed:', error);
  process.exit(1);
});
