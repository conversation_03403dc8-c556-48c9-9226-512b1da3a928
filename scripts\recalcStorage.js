// Recalculate storage usage for all users
console.log('🔧 Recalculating storage usage for all users...\n');

const { db } = require('../db/database');

async function recalcStorage() {
  try {
    console.log('1. Getting all users...');
    
    const users = await new Promise((resolve, reject) => {
      db.all('SELECT id, username FROM users', [], (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });

    console.log(`Found ${users.length} users\n`);

    for (const user of users) {
      console.log(`Processing ${user.username}...`);
      
      // Get all videos for this user
      const videos = await new Promise((resolve, reject) => {
        db.all(
          'SELECT file_size FROM videos WHERE user_id = ?',
          [user.id],
          (err, rows) => {
            if (err) reject(err);
            else resolve(rows);
          }
        );
      });

      // Calculate total storage in GB
      let totalBytes = 0;
      videos.forEach(video => {
        totalBytes += video.file_size || 0;
      });

      const totalGB = totalBytes / (1024 * 1024 * 1024);
      console.log(`  Videos: ${videos.length}, Total: ${totalGB.toFixed(3)}GB`);

      // Update user's storage usage
      await new Promise((resolve, reject) => {
        db.run(
          'UPDATE users SET used_storage_gb = ? WHERE id = ?',
          [totalGB, user.id],
          function(err) {
            if (err) {
              console.log(`  ❌ Failed: ${err.message}`);
              reject(err);
            } else {
              console.log(`  ✅ Updated to ${totalGB.toFixed(3)}GB`);
              resolve();
            }
          }
        );
      });
    }

    console.log('\n2. Final verification...');
    
    const updatedUsers = await new Promise((resolve, reject) => {
      db.all(
        'SELECT username, plan_type, max_storage_gb, used_storage_gb FROM users ORDER BY username',
        [],
        (err, rows) => {
          if (err) reject(err);
          else resolve(rows);
        }
      );
    });

    console.log('\nFinal storage status:');
    updatedUsers.forEach(user => {
      const percentage = user.max_storage_gb > 0 ? ((user.used_storage_gb || 0) / user.max_storage_gb * 100).toFixed(1) : 0;
      console.log(`${user.username} (${user.plan_type}): ${(user.used_storage_gb || 0).toFixed(3)}GB / ${user.max_storage_gb}GB (${percentage}%)`);
    });

    console.log('\n✅ Storage recalculation completed!');

  } catch (error) {
    console.error('❌ Error:', error);
  }
}

// Run the recalculation
recalcStorage().then(() => {
  console.log('\nDone!');
  process.exit(0);
}).catch(error => {
  console.error('Failed:', error);
  process.exit(1);
});
