const { db } = require('../db/database');
const Subscription = require('../models/Subscription');

async function checkPreviewPlanConfig() {
  console.log('🔍 Checking Preview Plan Configuration...\n');
  
  try {
    // 1. Check Preview plan in database
    console.log('1. Preview plan in subscription_plans table:');
    const previewPlan = await new Promise((resolve, reject) => {
      db.get('SELECT * FROM subscription_plans WHERE name = "Preview"', [], (err, row) => {
        if (err) reject(err);
        else resolve(row);
      });
    });

    if (previewPlan) {
      console.log(`   ✅ Preview plan found:`);
      console.log(`      - ID: ${previewPlan.id}`);
      console.log(`      - Name: ${previewPlan.name}`);
      console.log(`      - Streaming slots: ${previewPlan.max_streaming_slots}`);
      console.log(`      - Storage: ${previewPlan.max_storage_gb}GB`);
      console.log(`      - Price: $${previewPlan.price}`);
      console.log(`      - Active: ${previewPlan.is_active}`);
      console.log(`      - Features: ${previewPlan.features}`);
    } else {
      console.log('   ❌ Preview plan not found in database!');
      return;
    }

    // 2. Check Preview plan via Subscription model
    console.log('\n2. Preview plan via Subscription.getPlanByName():');
    const previewPlanModel = await Subscription.getPlanByName('Preview');
    
    if (previewPlanModel) {
      console.log(`   ✅ Preview plan found via model:`);
      console.log(`      - Streaming slots: ${previewPlanModel.max_streaming_slots}`);
      console.log(`      - Storage: ${previewPlanModel.max_storage_gb}GB`);
      console.log(`      - Features: ${JSON.stringify(previewPlanModel.features)}`);
    } else {
      console.log('   ❌ Preview plan not found via model!');
    }

    // 3. Check Preview plan users
    console.log('\n3. Users with Preview plan:');
    const previewUsers = await new Promise((resolve, reject) => {
      db.all(`
        SELECT id, username, plan_type, max_streaming_slots, max_storage_gb, 
               (SELECT COUNT(*) FROM streams WHERE user_id = users.id) as stream_count
        FROM users 
        WHERE plan_type = "Preview"
      `, [], (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });

    if (previewUsers.length > 0) {
      previewUsers.forEach(user => {
        console.log(`   - ${user.username}:`);
        console.log(`     • User slots: ${user.max_streaming_slots} (should be ${previewPlan.max_streaming_slots})`);
        console.log(`     • User storage: ${user.max_storage_gb}GB (should be ${previewPlan.max_storage_gb}GB)`);
        console.log(`     • Current streams: ${user.stream_count}`);
        
        // Check if user limits match plan limits
        if (user.max_streaming_slots !== previewPlan.max_streaming_slots) {
          console.log(`     ❌ User slots mismatch! User: ${user.max_streaming_slots}, Plan: ${previewPlan.max_streaming_slots}`);
        }
        if (Math.abs(user.max_storage_gb - previewPlan.max_storage_gb) > 0.001) {
          console.log(`     ❌ User storage mismatch! User: ${user.max_storage_gb}GB, Plan: ${previewPlan.max_storage_gb}GB`);
        }
      });
    } else {
      console.log('   ℹ️  No Preview plan users found');
    }

    // 4. Test quota check for Preview users
    console.log('\n4. Testing quota check for Preview users:');
    if (previewUsers.length > 0) {
      const testUser = previewUsers[0];
      console.log(`   Testing with user: ${testUser.username}`);
      
      const quotaCheck = await Subscription.checkStreamingSlotLimit(testUser.id);
      console.log(`   Quota check result:`);
      console.log(`      - Current slots: ${quotaCheck.currentSlots}`);
      console.log(`      - Max slots: ${quotaCheck.maxSlots}`);
      console.log(`      - Has limit: ${quotaCheck.hasLimit}`);
      
      if (quotaCheck.maxSlots === 0) {
        console.log(`   ✅ Quota check correctly shows 0 slots for Preview plan`);
      } else {
        console.log(`   ❌ Quota check shows ${quotaCheck.maxSlots} slots instead of 0!`);
        
        // Debug: Check what's happening in checkStreamingSlotLimit
        console.log(`\n   🔍 Debugging quota check...`);
        
        // Check if user has subscription
        const subscription = await Subscription.getUserSubscription(testUser.id);
        if (subscription) {
          console.log(`      - User has subscription: ${subscription.plan_name}`);
          console.log(`      - Subscription slots: ${subscription.max_streaming_slots}`);
        } else {
          console.log(`      - User has no subscription (using user plan limits)`);
          console.log(`      - User plan slots: ${testUser.max_streaming_slots}`);
        }
      }
    }

    // 5. Check if there are any inconsistencies
    console.log('\n5. Summary:');
    if (previewPlan.max_streaming_slots === 0) {
      console.log(`   ✅ Preview plan correctly configured with 0 streaming slots`);
    } else {
      console.log(`   ❌ Preview plan has ${previewPlan.max_streaming_slots} slots instead of 0!`);
      console.log(`   🔧 This needs to be fixed in the admin panel or database`);
    }

    // Check for users with incorrect limits
    const incorrectUsers = previewUsers.filter(user => 
      user.max_streaming_slots !== previewPlan.max_streaming_slots ||
      Math.abs(user.max_storage_gb - previewPlan.max_storage_gb) > 0.001
    );

    if (incorrectUsers.length > 0) {
      console.log(`   ❌ ${incorrectUsers.length} Preview users have incorrect limits`);
      console.log(`   🔧 These users need to be updated to match the Preview plan`);
    } else {
      console.log(`   ✅ All Preview users have correct limits`);
    }

  } catch (error) {
    console.error('❌ Error checking Preview plan config:', error);
  }
}

async function fixPreviewPlanIfNeeded() {
  console.log('\n🔧 Checking if Preview plan needs fixing...\n');
  
  try {
    const previewPlan = await new Promise((resolve, reject) => {
      db.get('SELECT * FROM subscription_plans WHERE name = "Preview"', [], (err, row) => {
        if (err) reject(err);
        else resolve(row);
      });
    });

    if (!previewPlan) {
      console.log('❌ Preview plan not found!');
      return;
    }

    if (previewPlan.max_streaming_slots !== 0) {
      console.log(`🔧 Fixing Preview plan slots: ${previewPlan.max_streaming_slots} → 0`);
      
      await new Promise((resolve, reject) => {
        db.run(
          'UPDATE subscription_plans SET max_streaming_slots = 0 WHERE name = "Preview"',
          [],
          (err) => {
            if (err) reject(err);
            else resolve();
          }
        );
      });
      
      console.log('✅ Preview plan slots fixed to 0');
    } else {
      console.log('✅ Preview plan already has 0 slots');
    }

    // Fix users with incorrect limits
    const previewUsers = await new Promise((resolve, reject) => {
      db.all('SELECT * FROM users WHERE plan_type = "Preview"', [], (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });

    const updatedPlan = await new Promise((resolve, reject) => {
      db.get('SELECT * FROM subscription_plans WHERE name = "Preview"', [], (err, row) => {
        if (err) reject(err);
        else resolve(row);
      });
    });

    for (const user of previewUsers) {
      if (user.max_streaming_slots !== updatedPlan.max_streaming_slots ||
          Math.abs(user.max_storage_gb - updatedPlan.max_storage_gb) > 0.001) {
        
        console.log(`🔧 Fixing user ${user.username}: ${user.max_streaming_slots}→${updatedPlan.max_streaming_slots} slots, ${user.max_storage_gb}→${updatedPlan.max_storage_gb}GB`);
        
        await new Promise((resolve, reject) => {
          db.run(
            'UPDATE users SET max_streaming_slots = ?, max_storage_gb = ? WHERE id = ?',
            [updatedPlan.max_streaming_slots, updatedPlan.max_storage_gb, user.id],
            (err) => {
              if (err) reject(err);
              else resolve();
            }
          );
        });
        
        console.log(`✅ Fixed user ${user.username}`);
      }
    }

    console.log('\n✅ Preview plan configuration check completed!');

  } catch (error) {
    console.error('❌ Error fixing Preview plan:', error);
  }
}

async function main() {
  await checkPreviewPlanConfig();
  await fixPreviewPlanIfNeeded();
  
  console.log('\n🎉 Preview plan check completed!');
  process.exit(0);
}

main().catch(error => {
  console.error('❌ Script failed:', error);
  process.exit(1);
});
