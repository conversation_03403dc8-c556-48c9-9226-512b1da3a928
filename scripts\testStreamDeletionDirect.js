const { db } = require('../db/database');
const Stream = require('../models/Stream');
const Subscription = require('../models/Subscription');

async function testStreamDeletionDirectly() {
  console.log('🚀 Testing Stream Deletion Directly...\n');
  
  try {
    // Find user with streams
    const usersWithStreams = await new Promise((resolve, reject) => {
      db.all(`
        SELECT u.id, u.username, u.plan_type, COUNT(s.id) as stream_count
        FROM users u
        LEFT JOIN streams s ON u.id = s.user_id
        GROUP BY u.id
        HAVING stream_count > 0
        ORDER BY stream_count DESC
        LIMIT 1
      `, [], (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });

    if (usersWithStreams.length === 0) {
      console.log('❌ No users with streams found');
      return;
    }

    const testUser = usersWithStreams[0];
    console.log(`📊 Testing with user: ${testUser.username}`);
    console.log(`   Plan: ${testUser.plan_type}`);
    console.log(`   Streams: ${testUser.stream_count}`);

    // Get detailed stream information
    const userStreams = await new Promise((resolve, reject) => {
      db.all('SELECT id, title, status FROM streams WHERE user_id = ?', [testUser.id], (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });

    console.log('\n📋 Current streams:');
    userStreams.forEach(stream => {
      console.log(`   - ${stream.title} (${stream.id}) - Status: ${stream.status}`);
    });

    // Test the direct deletion function
    console.log('\n🗑️ Testing direct stream deletion...');
    const deletionResult = await Stream.deleteAllUserStreams(testUser.id);
    
    console.log(`📊 Deletion result:`);
    console.log(`   - Success: ${deletionResult.success}`);
    console.log(`   - Streams deleted: ${deletionResult.deleted}`);
    
    if (deletionResult.streams && deletionResult.streams.length > 0) {
      console.log(`   - Deleted streams:`);
      deletionResult.streams.forEach(stream => {
        console.log(`     • ${stream.title} (${stream.id})`);
      });
    }

    // Verify deletion
    const remainingStreams = await new Promise((resolve, reject) => {
      db.all('SELECT * FROM streams WHERE user_id = ?', [testUser.id], (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });

    console.log(`\n✅ Verification: ${remainingStreams.length} streams remaining (should be 0)`);
    
    if (remainingStreams.length === 0) {
      console.log('🎉 Direct stream deletion test PASSED!');
    } else {
      console.log('❌ Direct stream deletion test FAILED!');
      remainingStreams.forEach(stream => {
        console.log(`   - Remaining: ${stream.title} (${stream.id})`);
      });
    }

    // Test quota check after deletion
    console.log('\n📊 Testing quota check after deletion...');
    const quotaCheck = await Subscription.checkStreamingSlotLimit(testUser.id);
    console.log(`   Current slots: ${quotaCheck.currentSlots}/${quotaCheck.maxSlots}`);
    console.log(`   Has limit: ${quotaCheck.hasLimit}`);

    if (quotaCheck.currentSlots === 0) {
      console.log('✅ Quota check shows 0 streams (correct)');
    } else {
      console.log('❌ Quota check still shows streams (incorrect)');
    }

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

async function testPreviewPlanDeletionFunction() {
  console.log('\n🔄 Testing Preview Plan Deletion Function...\n');
  
  try {
    // Create test streams for a Preview user
    const previewUsers = await new Promise((resolve, reject) => {
      db.all('SELECT * FROM users WHERE plan_type = "Preview" LIMIT 1', [], (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });

    if (previewUsers.length === 0) {
      console.log('❌ No Preview users found');
      return;
    }

    const testUser = previewUsers[0];
    console.log(`📊 Testing with Preview user: ${testUser.username}`);

    // Create test streams
    console.log('📝 Creating test streams...');
    const testStreams = [];
    for (let i = 1; i <= 2; i++) {
      const stream = await Stream.create({
        user_id: testUser.id,
        title: `Test Stream ${i}`,
        description: `Test stream for Preview deletion testing ${i}`,
        stream_key: `test-preview-key-${i}-${Date.now()}`,
        status: 'offline'
      });
      testStreams.push(stream);
      console.log(`   ✅ Created test stream: ${stream.title}`);
    }

    // Check streams before deletion
    const streamsBefore = await new Promise((resolve, reject) => {
      db.all('SELECT COUNT(*) as count FROM streams WHERE user_id = ?', [testUser.id], (err, rows) => {
        if (err) reject(err);
        else resolve(rows[0].count);
      });
    });

    console.log(`\n📊 Before deletion: ${streamsBefore} streams`);

    // Test the Preview plan deletion function
    console.log('\n🗑️ Testing Preview plan deletion function...');
    const deletionResult = await Subscription.deleteAllUserStreamsForPreviewPlan(testUser.id);

    console.log(`📊 Deletion result:`);
    console.log(`   - Success: ${deletionResult.success}`);
    console.log(`   - Streams deleted: ${deletionResult.deleted}`);

    // Check streams after deletion
    const streamsAfter = await new Promise((resolve, reject) => {
      db.all('SELECT COUNT(*) as count FROM streams WHERE user_id = ?', [testUser.id], (err, rows) => {
        if (err) reject(err);
        else resolve(rows[0].count);
      });
    });

    console.log(`\n📊 After deletion: ${streamsAfter} streams`);

    if (streamsAfter === 0 && deletionResult.deleted === streamsBefore) {
      console.log('🎉 Preview plan deletion function test PASSED!');
    } else {
      console.log('❌ Preview plan deletion function test FAILED!');
      console.log(`   Expected: 0 streams remaining, ${streamsBefore} deleted`);
      console.log(`   Actual: ${streamsAfter} streams remaining, ${deletionResult.deleted} deleted`);
    }

    // Test quota check
    console.log('\n📊 Testing quota check...');
    const quotaCheck = await Subscription.checkStreamingSlotLimit(testUser.id);
    console.log(`   Current slots: ${quotaCheck.currentSlots}/${quotaCheck.maxSlots}`);

    if (quotaCheck.currentSlots === 0) {
      console.log('✅ Quota check shows 0/0 streams (correct for Preview plan)');
    } else {
      console.log('❌ Quota check still shows streams (incorrect)');
    }

  } catch (error) {
    console.error('❌ Preview plan deletion test failed:', error);
  }
}

async function main() {
  console.log('🚀 Testing Stream Deletion Functions...\n');
  
  await testStreamDeletionDirectly();
  await testPreviewPlanDeletionFunction();
  
  console.log('\n🎉 All tests completed!');
  process.exit(0);
}

main().catch(error => {
  console.error('❌ Test script failed:', error);
  process.exit(1);
});
