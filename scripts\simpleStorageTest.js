// Simple storage test
const { db } = require('../db/database');
const Subscription = require('../models/Subscription');

async function test() {
  console.log('Testing storage update functions...');
  
  try {
    // Get first user
    const user = await new Promise((resolve, reject) => {
      db.get('SELECT id, username, used_storage_gb FROM users LIMIT 1', [], (err, row) => {
        if (err) reject(err);
        else resolve(row);
      });
    });

    if (!user) {
      console.log('No users found');
      return;
    }

    console.log(`User: ${user.username}`);
    console.log(`Before: ${user.used_storage_gb || 0}GB`);

    // Test add
    await Subscription.updateStorageUsage(user.id, 0.001);
    console.log('✅ Added 0.001GB');

    // Test subtract
    await Subscription.updateStorageUsage(user.id, -0.001);
    console.log('✅ Subtracted 0.001GB');

    console.log('Storage functions working!');

  } catch (error) {
    console.error('Error:', error);
  }
}

test().then(() => {
  console.log('Done');
  process.exit(0);
}).catch(err => {
  console.error('Failed:', err);
  process.exit(1);
});
