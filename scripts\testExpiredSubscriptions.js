const { db } = require('../db/database');

async function testScript() {
  console.log('🚀 Testing expired subscription script...');
  
  try {
    // Test database connection
    console.log('📊 Testing database connection...');
    
    const result = await new Promise((resolve, reject) => {
      db.get('SELECT COUNT(*) as count FROM user_subscriptions', [], (err, row) => {
        if (err) reject(err);
        else resolve(row);
      });
    });
    
    console.log(`✅ Database connected. Total subscriptions: ${result.count}`);
    
    // Check for expired subscriptions
    const expiredSubscriptions = await new Promise((resolve, reject) => {
      db.all(`
        SELECT us.*, sp.name as plan_name, u.username
        FROM user_subscriptions us
        JOIN subscription_plans sp ON us.plan_id = sp.id
        JOIN users u ON us.user_id = u.id
        WHERE us.status = 'active' 
        AND us.end_date < datetime('now')
        AND sp.name != 'Preview'
      `, [], (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });

    console.log(`📋 Found ${expiredSubscriptions.length} expired subscription(s)`);
    
    if (expiredSubscriptions.length > 0) {
      expiredSubscriptions.forEach(sub => {
        console.log(`   - User: ${sub.username}, Plan: ${sub.plan_name}, Expired: ${sub.end_date}`);
      });
    }
    
    // Check for Preview plan subscriptions
    const previewSubscriptions = await new Promise((resolve, reject) => {
      db.all(`
        SELECT us.*, sp.name as plan_name, u.username
        FROM user_subscriptions us
        JOIN subscription_plans sp ON us.plan_id = sp.id
        JOIN users u ON us.user_id = u.id
        WHERE us.status = 'active' 
        AND sp.name = 'Preview'
      `, [], (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });

    console.log(`📋 Found ${previewSubscriptions.length} Preview plan subscription(s)`);
    
    if (previewSubscriptions.length > 0) {
      previewSubscriptions.forEach(sub => {
        console.log(`   - User: ${sub.username}, Plan: ${sub.plan_name}`);
      });
    }
    
    console.log('✅ Test completed successfully!');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
  
  process.exit(0);
}

testScript();
