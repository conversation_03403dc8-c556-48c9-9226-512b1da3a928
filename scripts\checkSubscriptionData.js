const { db } = require('../db/database');

async function checkSubscriptionData() {
  console.log('🔍 Checking subscription data...\n');

  try {
    // Check user_subscriptions table
    const userSubscriptions = await new Promise((resolve, reject) => {
      db.all('SELECT * FROM user_subscriptions', [], (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });

    console.log('📋 user_subscriptions table:');
    console.log(`Found ${userSubscriptions.length} subscription records`);
    userSubscriptions.forEach(sub => {
      console.log(`- User: ${sub.user_id}, Plan: ${sub.plan_id}, Status: ${sub.status}, Created: ${sub.created_at}`);
    });

    // Check users table for plan_type
    const users = await new Promise((resolve, reject) => {
      db.all('SELECT id, username, plan_type FROM users', [], (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });

    console.log('\n👥 users table plan_type:');
    users.forEach(user => {
      console.log(`- User: ${user.username}, Plan: ${user.plan_type}`);
    });

    // Check the query used in admin stats
    const adminStats = await new Promise((resolve, reject) => {
      db.all(`
        SELECT
          (SELECT COUNT(*) FROM users) as total_users,
          (SELECT COUNT(*) FROM users WHERE is_active = 1) as active_users,
          (SELECT COUNT(*) FROM streams) as total_streams,
          (SELECT COUNT(*) FROM streams WHERE status = 'live') as live_streams,
          (SELECT COUNT(*) FROM videos) as total_videos,
          (SELECT SUM(file_size) FROM videos) as total_storage_bytes,
          (SELECT COUNT(*) FROM user_subscriptions WHERE status = 'active') as active_subscriptions
      `, [], (err, rows) => {
        if (err) reject(err);
        else resolve(rows[0]);
      });
    });

    console.log('\n📊 Admin stats query results:');
    console.log('- Total users:', adminStats.total_users);
    console.log('- Active users:', adminStats.active_users);
    console.log('- Total streams:', adminStats.total_streams);
    console.log('- Live streams:', adminStats.live_streams);
    console.log('- Total videos:', adminStats.total_videos);
    console.log('- Active subscriptions:', adminStats.active_subscriptions);

    // Check subscription plans with subscriber count
    const plansWithSubscribers = await new Promise((resolve, reject) => {
      db.all(`
        SELECT 
          sp.*,
          COUNT(u.id) as subscriber_count
        FROM subscription_plans sp
        LEFT JOIN users u ON u.plan_type = sp.name
        GROUP BY sp.id
        ORDER BY sp.price ASC
      `, [], (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });

    console.log('\n📋 Plans with subscriber count (based on users.plan_type):');
    plansWithSubscribers.forEach(plan => {
      console.log(`- ${plan.name}: ${plan.subscriber_count} subscribers`);
    });

    console.log('\n✅ Subscription data check completed!');
  } catch (error) {
    console.error('❌ Error checking subscription data:', error);
  }

  process.exit(0);
}

checkSubscriptionData();
