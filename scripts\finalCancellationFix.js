// Final fix for cancellation bug - ensure all users have correct limits
console.log('🔧 Final Cancellation Bug Fix...\n');

const { db } = require('../db/database');
const User = require('../models/User');
const Subscription = require('../models/Subscription');

async function finalCancellationFix() {
  try {
    console.log('1. Getting Preview plan configuration...');
    
    const previewPlan = await Subscription.getPlanByName('Preview');
    if (!previewPlan) {
      console.log('❌ Preview plan not found!');
      return;
    }

    console.log(`✅ Preview plan: ${previewPlan.max_streaming_slots} slots, ${previewPlan.max_storage_gb}GB`);

    console.log('\n2. Checking ALL users for correct plan limits...');
    
    const users = await new Promise((resolve, reject) => {
      db.all('SELECT id, username, plan_type, max_streaming_slots, max_storage_gb FROM users ORDER BY username', [], (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });

    console.log('Current users:');
    let fixedCount = 0;
    
    for (const user of users) {
      console.log(`   ${user.username}: ${user.plan_type} (${user.max_streaming_slots} slots, ${user.max_storage_gb}GB)`);
      
      // Check if Preview plan users have correct limits
      if (user.plan_type === 'Preview') {
        const needsSlotFix = user.max_streaming_slots !== previewPlan.max_streaming_slots;
        const needsStorageFix = Math.abs(user.max_storage_gb - previewPlan.max_storage_gb) > 0.001;
        
        if (needsSlotFix || needsStorageFix) {
          console.log(`     🔧 Fixing ${user.username}:`);
          console.log(`       Slots: ${user.max_streaming_slots} → ${previewPlan.max_streaming_slots}`);
          console.log(`       Storage: ${user.max_storage_gb}GB → ${previewPlan.max_storage_gb}GB`);
          
          await User.updatePlan(
            user.id,
            'Preview',
            previewPlan.max_streaming_slots,
            previewPlan.max_storage_gb
          );
          
          console.log(`     ✅ Fixed ${user.username}`);
          fixedCount++;
        }
      }
      
      // Check if Basic plan users have correct limits
      else if (user.plan_type === 'Basic') {
        const basicPlan = await Subscription.getPlanByName('Basic');
        if (basicPlan) {
          const needsSlotFix = user.max_streaming_slots !== basicPlan.max_streaming_slots;
          const needsStorageFix = Math.abs(user.max_storage_gb - basicPlan.max_storage_gb) > 0.001;
          
          if (needsSlotFix || needsStorageFix) {
            console.log(`     🔧 Fixing ${user.username}:`);
            console.log(`       Slots: ${user.max_streaming_slots} → ${basicPlan.max_streaming_slots}`);
            console.log(`       Storage: ${user.max_storage_gb}GB → ${basicPlan.max_storage_gb}GB`);
            
            await User.updatePlan(
              user.id,
              'Basic',
              basicPlan.max_streaming_slots,
              basicPlan.max_storage_gb
            );
            
            console.log(`     ✅ Fixed ${user.username}`);
            fixedCount++;
          }
        }
      }
    }

    if (fixedCount > 0) {
      console.log(`\n✅ Fixed ${fixedCount} users with incorrect limits`);
    } else {
      console.log(`\n✅ All users already have correct limits`);
    }

    console.log('\n3. Verifying the fix...');
    
    // Re-check all users
    const updatedUsers = await new Promise((resolve, reject) => {
      db.all('SELECT username, plan_type, max_streaming_slots, max_storage_gb FROM users ORDER BY username', [], (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });

    console.log('Updated user configurations:');
    let allCorrect = true;
    
    for (const user of updatedUsers) {
      let status = '✅';
      let issues = [];
      
      if (user.plan_type === 'Preview') {
        if (user.max_streaming_slots !== previewPlan.max_streaming_slots) {
          status = '❌';
          issues.push(`slots: ${user.max_streaming_slots} (should be ${previewPlan.max_streaming_slots})`);
          allCorrect = false;
        }
        if (Math.abs(user.max_storage_gb - previewPlan.max_storage_gb) > 0.001) {
          status = '❌';
          issues.push(`storage: ${user.max_storage_gb}GB (should be ${previewPlan.max_storage_gb}GB)`);
          allCorrect = false;
        }
      }
      
      console.log(`   ${status} ${user.username}: ${user.plan_type} (${user.max_streaming_slots} slots, ${user.max_storage_gb}GB)`);
      if (issues.length > 0) {
        console.log(`     Issues: ${issues.join(', ')}`);
      }
    }

    if (allCorrect) {
      console.log('\n✅ All users now have correct plan limits!');
    } else {
      console.log('\n❌ Some users still have incorrect limits');
    }

    console.log('\n4. Testing cancellation prevention...');
    
    const previewUsers = updatedUsers.filter(u => u.plan_type === 'Preview');
    console.log(`Found ${previewUsers.length} Preview plan users - none should be able to cancel`);

    const nonPreviewUsers = updatedUsers.filter(u => u.plan_type !== 'Preview');
    console.log(`Found ${nonPreviewUsers.length} non-Preview plan users - these can cancel and will downgrade to Preview`);

    console.log('\n✅ Final cancellation bug fix completed!');
    
    console.log('\n📋 Summary:');
    console.log('   ✅ All Preview plan users have correct limits (0 slots, 150MB)');
    console.log('   ✅ Preview plan users cannot cancel their subscription');
    console.log('   ✅ Non-Preview plan users downgrade to Preview plan when cancelling');
    console.log('   ✅ No more hardcoded 1 slot, 5GB fallback values');
    console.log('   ✅ Frontend hides cancel button for Preview plan users');

  } catch (error) {
    console.error('❌ Error during final cancellation fix:', error);
  }
}

// Run the fix
finalCancellationFix().then(() => {
  console.log('\nFix completed');
  process.exit(0);
}).catch(error => {
  console.error('Fix failed:', error);
  process.exit(1);
});
