// Test storage error messages for user-friendly display
console.log('🧪 Testing Storage Error Messages...\n');

const { db } = require('../db/database');
const QuotaMiddleware = require('../middleware/quotaMiddleware');

async function testStorageErrorMessages() {
  try {
    console.log('1. Testing storage error message formatting...');
    
    // Get a Preview plan user for testing
    const previewUser = await new Promise((resolve, reject) => {
      db.get('SELECT * FROM users WHERE plan_type = ? LIMIT 1', ['Preview'], (err, row) => {
        if (err) reject(err);
        else resolve(row);
      });
    });

    if (!previewUser) {
      console.log('   ❌ No Preview plan user found for testing');
      return;
    }

    console.log(`   Testing with user: ${previewUser.username} (${previewUser.plan_type})`);
    console.log(`   Max storage: ${previewUser.max_storage_gb}GB`);
    console.log(`   Used storage: ${previewUser.used_storage_gb || 0}GB`);

    // Test different file sizes and their error messages
    const testCases = [
      {
        name: 'Small file (10MB)',
        sizeBytes: 10 * 1024 * 1024,
        sizeGB: 0.01
      },
      {
        name: 'Medium file (50MB)', 
        sizeBytes: 50 * 1024 * 1024,
        sizeGB: 0.05
      },
      {
        name: 'Large file (100MB)',
        sizeBytes: 100 * 1024 * 1024,
        sizeGB: 0.1
      },
      {
        name: 'Very large file (200MB)',
        sizeBytes: 200 * 1024 * 1024,
        sizeGB: 0.2
      }
    ];

    for (const testCase of testCases) {
      console.log(`\n   Testing ${testCase.name}:`);
      
      // Mock request and response objects
      const mockReq = {
        session: { userId: previewUser.id },
        file: { size: testCase.sizeBytes }
      };

      const mockRes = {
        status: (code) => ({
          json: (data) => {
            console.log(`     Status: ${code}`);
            if (code === 413) {
              console.log(`     ✅ Error message: "${data.details.message}"`);
              
              // Verify the message contains user-friendly units
              const message = data.details.message;
              if (message.includes('MB') || message.includes('GB')) {
                console.log(`     ✅ Message uses user-friendly units`);
              } else {
                console.log(`     ⚠️ Message doesn't use user-friendly units`);
              }
            } else {
              console.log(`     ✅ Upload would be allowed`);
            }
            return { json: () => {} };
          }
        })
      };

      let middlewareCalled = false;
      const mockNext = () => {
        middlewareCalled = true;
        console.log(`     ✅ Upload would proceed`);
      };

      // Test the storage quota middleware
      try {
        const middleware = QuotaMiddleware.checkStorageQuota();
        await middleware(mockReq, mockRes, mockNext);
        
        if (!middlewareCalled) {
          // Middleware blocked the request (returned early)
          console.log(`     ✅ Upload blocked by quota check`);
        }
      } catch (error) {
        console.log(`     ❌ Error in middleware: ${error.message}`);
      }
    }

    console.log('\n2. Testing edge cases...');

    // Test with exactly at limit
    const availableStorage = previewUser.max_storage_gb - (previewUser.used_storage_gb || 0);
    const exactLimitBytes = Math.floor(availableStorage * 1024 * 1024 * 1024);
    
    console.log(`\n   Testing file exactly at limit (${Math.round(availableStorage * 1024)}MB):`);
    
    const mockReqExact = {
      session: { userId: previewUser.id },
      file: { size: exactLimitBytes }
    };

    const mockResExact = {
      status: (code) => ({
        json: (data) => {
          console.log(`     Status: ${code}`);
          if (code === 413) {
            console.log(`     ✅ Error message: "${data.details.message}"`);
          } else {
            console.log(`     ✅ Upload allowed`);
          }
          return { json: () => {} };
        }
      })
    };

    let exactMiddlewareCalled = false;
    const mockNextExact = () => {
      exactMiddlewareCalled = true;
      console.log(`     ✅ Upload would proceed`);
    };

    try {
      const middleware = QuotaMiddleware.checkStorageQuota();
      await middleware(mockReqExact, mockResExact, mockNextExact);
    } catch (error) {
      console.log(`     ❌ Error in middleware: ${error.message}`);
    }

    console.log('\n3. Testing message format consistency...');
    
    // Test that messages are consistent and user-friendly
    const formatStorage = (sizeGB) => {
      if (sizeGB < 1) {
        return `${Math.round(sizeGB * 1024)}MB`;
      } else {
        return `${sizeGB.toFixed(2)}GB`;
      }
    };

    console.log('   Format examples:');
    console.log(`     0.001GB → ${formatStorage(0.001)}`);
    console.log(`     0.05GB → ${formatStorage(0.05)}`);
    console.log(`     0.5GB → ${formatStorage(0.5)}`);
    console.log(`     1.0GB → ${formatStorage(1.0)}`);
    console.log(`     2.5GB → ${formatStorage(2.5)}`);

    console.log('\n✅ Storage error message testing completed!');

  } catch (error) {
    console.error('❌ Error during storage error message testing:', error);
  }
}

// Run the test
testStorageErrorMessages().then(() => {
  console.log('\nTesting completed');
  process.exit(0);
}).catch(error => {
  console.error('Testing failed:', error);
  process.exit(1);
});
