const { db } = require('../db/database');
const Subscription = require('../models/Subscription');

async function testQuotaCheckFix() {
  console.log('🔍 Testing Quota Check Fix for Preview Plan...\n');
  
  try {
    // Test Preview plan users
    const previewUsers = await new Promise((resolve, reject) => {
      db.all(`
        SELECT id, username, plan_type, max_streaming_slots, max_storage_gb,
               (SELECT COUNT(*) FROM streams WHERE user_id = users.id) as stream_count
        FROM users 
        WHERE plan_type = "Preview"
      `, [], (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });

    console.log('📊 Testing Preview Plan Users:');
    
    for (const user of previewUsers) {
      console.log(`\n👤 User: ${user.username}`);
      console.log(`   Plan: ${user.plan_type}`);
      console.log(`   User limits: ${user.max_streaming_slots} slots, ${user.max_storage_gb}GB`);
      console.log(`   Current streams: ${user.stream_count}`);
      
      // Test quota check
      const quotaCheck = await Subscription.checkStreamingSlotLimit(user.id);
      console.log(`   Quota check result: ${quotaCheck.currentSlots}/${quotaCheck.maxSlots} slots`);
      
      // Check if it's correct
      if (quotaCheck.maxSlots === 0) {
        console.log(`   ✅ Correct: Preview plan shows 0 slots`);
      } else {
        console.log(`   ❌ Incorrect: Preview plan shows ${quotaCheck.maxSlots} slots instead of 0`);
      }
      
      // Check if user has subscription
      const subscription = await Subscription.getUserSubscription(user.id);
      if (subscription) {
        console.log(`   ⚠️ User has active subscription: ${subscription.plan_name}`);
      } else {
        console.log(`   ✅ User has no active subscription (using user plan limits)`);
      }
    }

    // Test Basic plan users for comparison
    const basicUsers = await new Promise((resolve, reject) => {
      db.all(`
        SELECT id, username, plan_type, max_streaming_slots, max_storage_gb,
               (SELECT COUNT(*) FROM streams WHERE user_id = users.id) as stream_count
        FROM users 
        WHERE plan_type = "Basic"
        LIMIT 1
      `, [], (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });

    if (basicUsers.length > 0) {
      console.log('\n📊 Testing Basic Plan Users (for comparison):');
      
      for (const user of basicUsers) {
        console.log(`\n👤 User: ${user.username}`);
        console.log(`   Plan: ${user.plan_type}`);
        console.log(`   User limits: ${user.max_streaming_slots} slots, ${user.max_storage_gb}GB`);
        console.log(`   Current streams: ${user.stream_count}`);
        
        // Test quota check
        const quotaCheck = await Subscription.checkStreamingSlotLimit(user.id);
        console.log(`   Quota check result: ${quotaCheck.currentSlots}/${quotaCheck.maxSlots} slots`);
        
        // Check if user has subscription
        const subscription = await Subscription.getUserSubscription(user.id);
        if (subscription) {
          console.log(`   ✅ User has active subscription: ${subscription.plan_name}`);
        } else {
          console.log(`   ✅ User has no active subscription (using user plan limits)`);
        }
      }
    }

    // Summary
    console.log('\n📋 Summary:');
    
    const previewUsersWithCorrectQuota = previewUsers.filter(async user => {
      const quotaCheck = await Subscription.checkStreamingSlotLimit(user.id);
      return quotaCheck.maxSlots === 0;
    });

    // Test all Preview users again to get accurate count
    let correctCount = 0;
    let incorrectCount = 0;
    
    for (const user of previewUsers) {
      const quotaCheck = await Subscription.checkStreamingSlotLimit(user.id);
      if (quotaCheck.maxSlots === 0) {
        correctCount++;
      } else {
        incorrectCount++;
      }
    }

    console.log(`✅ Preview users with correct quota (0 slots): ${correctCount}`);
    console.log(`❌ Preview users with incorrect quota: ${incorrectCount}`);
    
    if (incorrectCount === 0) {
      console.log('\n🎉 All Preview plan users now show correct quota (0/0 slots)!');
      console.log('✅ Bug fix successful!');
    } else {
      console.log('\n⚠️ Some Preview plan users still show incorrect quota');
      console.log('❌ Bug fix needs more work');
    }

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

async function testQuotaCheckWithDifferentScenarios() {
  console.log('\n🧪 Testing Quota Check with Different Scenarios...\n');
  
  try {
    // Scenario 1: Preview user with no subscription
    console.log('1. Preview user with no subscription:');
    const previewUser = await new Promise((resolve, reject) => {
      db.get('SELECT * FROM users WHERE plan_type = "Preview" LIMIT 1', [], (err, row) => {
        if (err) reject(err);
        else resolve(row);
      });
    });

    if (previewUser) {
      const quotaCheck = await Subscription.checkStreamingSlotLimit(previewUser.id);
      console.log(`   Result: ${quotaCheck.currentSlots}/${quotaCheck.maxSlots} slots`);
      console.log(`   Expected: X/0 slots`);
      console.log(`   Status: ${quotaCheck.maxSlots === 0 ? '✅ PASS' : '❌ FAIL'}`);
    }

    // Scenario 2: User with active subscription
    console.log('\n2. User with active subscription:');
    const userWithSubscription = await new Promise((resolve, reject) => {
      db.get(`
        SELECT u.*, us.id as subscription_id, sp.name as subscription_plan
        FROM users u
        JOIN user_subscriptions us ON u.id = us.user_id
        JOIN subscription_plans sp ON us.plan_id = sp.id
        WHERE us.status = 'active' AND sp.name != 'Preview'
        LIMIT 1
      `, [], (err, row) => {
        if (err) reject(err);
        else resolve(row);
      });
    });

    if (userWithSubscription) {
      const quotaCheck = await Subscription.checkStreamingSlotLimit(userWithSubscription.id);
      console.log(`   User: ${userWithSubscription.username} (${userWithSubscription.subscription_plan})`);
      console.log(`   Result: ${quotaCheck.currentSlots}/${quotaCheck.maxSlots} slots`);
      console.log(`   Status: ✅ Using subscription limits`);
    } else {
      console.log('   No users with active subscriptions found');
    }

    // Scenario 3: Test error handling
    console.log('\n3. Error handling (invalid user ID):');
    const errorQuotaCheck = await Subscription.checkStreamingSlotLimit('invalid-user-id');
    console.log(`   Result: ${errorQuotaCheck.currentSlots}/${errorQuotaCheck.maxSlots} slots`);
    console.log(`   Expected: 0/0 slots`);
    console.log(`   Status: ${errorQuotaCheck.maxSlots === 0 ? '✅ PASS' : '❌ FAIL'}`);

  } catch (error) {
    console.error('❌ Scenario test failed:', error);
  }
}

async function main() {
  await testQuotaCheckFix();
  await testQuotaCheckWithDifferentScenarios();
  
  console.log('\n🎯 Quota check fix testing completed!');
  process.exit(0);
}

main().catch(error => {
  console.error('❌ Script failed:', error);
  process.exit(1);
});
