const { db } = require('../db/database');
const Subscription = require('../models/Subscription');
const User = require('../models/User');

async function handleExpiredSubscriptions() {
  console.log('🔍 Checking for expired subscriptions...');
  
  try {
    // Find all active subscriptions that have expired
    const expiredSubscriptions = await new Promise((resolve, reject) => {
      db.all(`
        SELECT us.*, sp.name as plan_name, u.username
        FROM user_subscriptions us
        JOIN subscription_plans sp ON us.plan_id = sp.id
        JOIN users u ON us.user_id = u.id
        WHERE us.status = 'active' 
        AND us.end_date < datetime('now')
        AND sp.name != 'Preview'
      `, [], (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });

    if (expiredSubscriptions.length === 0) {
      console.log('✅ No expired subscriptions found');
      return;
    }

    console.log(`📋 Found ${expiredSubscriptions.length} expired subscription(s)`);

    for (const subscription of expiredSubscriptions) {
      try {
        console.log(`\n🔄 Processing expired subscription for user: ${subscription.username}`);
        console.log(`   Plan: ${subscription.plan_name}`);
        console.log(`   Expired: ${subscription.end_date}`);

        // Mark subscription as expired
        await Subscription.updateSubscriptionStatus(subscription.id, 'expired');
        console.log('   ✅ Subscription marked as expired');

        // Downgrade user to Preview plan
        const success = await Subscription.handleExpiredSubscription(subscription.user_id);
        if (success) {
          console.log('   ✅ User downgraded to Preview plan');
        } else {
          console.log('   ❌ Failed to downgrade user');
        }

      } catch (error) {
        console.error(`   ❌ Error processing subscription for ${subscription.username}:`, error.message);
      }
    }

    console.log('\n✅ Expired subscription handling completed');

  } catch (error) {
    console.error('❌ Error handling expired subscriptions:', error);
  }
}

async function checkPreviewPlanSubscriptions() {
  console.log('\n🔍 Checking for Preview plan subscriptions...');
  
  try {
    // Find all active Preview plan subscriptions (these should not exist)
    const previewSubscriptions = await new Promise((resolve, reject) => {
      db.all(`
        SELECT us.*, sp.name as plan_name, u.username
        FROM user_subscriptions us
        JOIN subscription_plans sp ON us.plan_id = sp.id
        JOIN users u ON us.user_id = u.id
        WHERE us.status = 'active' 
        AND sp.name = 'Preview'
      `, [], (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });

    if (previewSubscriptions.length === 0) {
      console.log('✅ No Preview plan subscriptions found (this is correct)');
      return;
    }

    console.log(`📋 Found ${previewSubscriptions.length} Preview plan subscription(s) - these will be cancelled`);

    for (const subscription of previewSubscriptions) {
      try {
        console.log(`\n🔄 Cancelling Preview plan subscription for user: ${subscription.username}`);

        // Cancel Preview plan subscription (Preview should not have subscriptions)
        await Subscription.updateSubscriptionStatus(subscription.id, 'cancelled');
        console.log('   ✅ Preview plan subscription cancelled');

      } catch (error) {
        console.error(`   ❌ Error cancelling Preview subscription for ${subscription.username}:`, error.message);
      }
    }

    console.log('\n✅ Preview plan subscription cleanup completed');

  } catch (error) {
    console.error('❌ Error checking Preview plan subscriptions:', error);
  }
}

async function main() {
  console.log('🚀 Starting expired subscription handler...\n');
  
  await handleExpiredSubscriptions();
  await checkPreviewPlanSubscriptions();
  
  console.log('\n🎉 All tasks completed!');
  process.exit(0);
}

// Run if called directly
if (require.main === module) {
  main().catch(error => {
    console.error('❌ Script failed:', error);
    process.exit(1);
  });
}

module.exports = {
  handleExpiredSubscriptions,
  checkPreviewPlanSubscriptions
};
