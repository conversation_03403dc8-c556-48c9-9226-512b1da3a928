// Test quota exceeded error message
console.log('🧪 Testing Quota Exceeded Error Message...\n');

const { db } = require('../db/database');
const QuotaMiddleware = require('../middleware/quotaMiddleware');

async function testQuotaExceededMessage() {
  try {
    // Get a Preview plan user
    const previewUser = await new Promise((resolve, reject) => {
      db.get('SELECT * FROM users WHERE plan_type = ? LIMIT 1', ['Preview'], (err, row) => {
        if (err) reject(err);
        else resolve(row);
      });
    });

    if (!previewUser) {
      console.log('❌ No Preview plan user found');
      return;
    }

    console.log(`Testing with user: ${previewUser.username}`);
    console.log(`Max storage: ${previewUser.max_storage_gb}GB`);
    console.log(`Used storage: ${previewUser.used_storage_gb || 0}GB`);

    // Calculate available storage
    const availableStorage = previewUser.max_storage_gb - (previewUser.used_storage_gb || 0);
    console.log(`Available storage: ${availableStorage.toFixed(3)}GB`);

    // Test with a file that would exceed the limit
    const excessiveFileSize = (availableStorage + 0.1) * 1024 * 1024 * 1024; // Add 100MB over limit
    
    console.log(`\nTesting with file size: ${Math.round(excessiveFileSize / (1024 * 1024))}MB (exceeds limit)`);

    // Mock request and response
    const mockReq = {
      session: { userId: previewUser.id },
      file: { size: excessiveFileSize }
    };

    let errorResponse = null;
    const mockRes = {
      status: (code) => ({
        json: (data) => {
          errorResponse = { status: code, data };
          return { json: () => {} };
        }
      })
    };

    const mockNext = () => {
      console.log('❌ Upload should have been blocked');
    };

    // Test the middleware
    const middleware = QuotaMiddleware.checkStorageQuota();
    await middleware(mockReq, mockRes, mockNext);

    if (errorResponse) {
      console.log(`\n✅ Status Code: ${errorResponse.status}`);
      console.log(`✅ Error Message: "${errorResponse.data.details.message}"`);
      
      // Verify the message is user-friendly
      const message = errorResponse.data.details.message;
      if (message.includes('MB') || message.includes('GB')) {
        console.log('✅ Message uses user-friendly units');
      }
      
      if (message.includes('Upload failed:')) {
        console.log('✅ Message starts with clear failure indication');
      }
      
      if (message.includes('available')) {
        console.log('✅ Message shows available storage');
      }
      
      if (message.includes('total')) {
        console.log('✅ Message shows total storage');
      }
    } else {
      console.log('❌ No error response received');
    }

    console.log('\n✅ Quota exceeded message test completed!');

  } catch (error) {
    console.error('❌ Error during test:', error);
  }
}

// Run the test
testQuotaExceededMessage().then(() => {
  console.log('\nTest completed');
  process.exit(0);
}).catch(error => {
  console.error('Test failed:', error);
  process.exit(1);
});
