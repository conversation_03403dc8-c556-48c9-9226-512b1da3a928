const { db } = require('../db/database');

async function fixPlatformIcons() {
  console.log('🔧 Fixing platform icons in existing streams...\n');

  try {
    // Get all streams
    const streams = await new Promise((resolve, reject) => {
      db.all('SELECT id, platform, platform_icon, rtmp_url FROM streams', (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });

    console.log(`📋 Found ${streams.length} streams to check`);

    let fixedCount = 0;

    for (const stream of streams) {
      let needsUpdate = false;
      let newPlatform = stream.platform;
      let newPlatformIcon = stream.platform_icon;

      // Determine correct platform and icon based on RTMP URL
      if (stream.rtmp_url) {
        if (stream.rtmp_url.includes('youtube.com')) {
          newPlatform = 'YouTube';
          newPlatformIcon = 'ti-brand-youtube';
        } else if (stream.rtmp_url.includes('facebook.com')) {
          newPlatform = 'Facebook';
          newPlatformIcon = 'ti-brand-facebook';
        } else if (stream.rtmp_url.includes('twitch.tv')) {
          newPlatform = 'Twitch';
          newPlatformIcon = 'ti-brand-twitch';
        } else if (stream.rtmp_url.includes('tiktok.com')) {
          newPlatform = 'TikTok';
          newPlatformIcon = 'ti-brand-tiktok';
        } else if (stream.rtmp_url.includes('instagram.com')) {
          newPlatform = 'Instagram';
          newPlatformIcon = 'ti-brand-instagram';
        } else if (stream.rtmp_url.includes('shopee.io')) {
          newPlatform = 'Shopee Live';
          newPlatformIcon = 'ti-shopping-bag';
        } else if (stream.rtmp_url.includes('restream.io')) {
          newPlatform = 'Restream.io';
          newPlatformIcon = 'ti-live-photo';
        } else {
          newPlatform = 'Custom';
          newPlatformIcon = 'ti-broadcast';
        }
      }

      // Check if update is needed
      if (stream.platform !== newPlatform || stream.platform_icon !== newPlatformIcon) {
        needsUpdate = true;
      }

      // Fix specific known issues
      if (stream.platform_icon === 'ti-brand-shopee') {
        newPlatformIcon = 'ti-shopping-bag';
        needsUpdate = true;
      }

      if (needsUpdate) {
        console.log(`🔄 Updating stream "${stream.id}": ${stream.platform} (${stream.platform_icon}) → ${newPlatform} (${newPlatformIcon})`);
        
        await new Promise((resolve, reject) => {
          db.run(
            'UPDATE streams SET platform = ?, platform_icon = ? WHERE id = ?',
            [newPlatform, newPlatformIcon, stream.id],
            function(err) {
              if (err) reject(err);
              else resolve();
            }
          );
        });

        fixedCount++;
      }
    }

    console.log(`\n✅ Platform icon fix completed!`);
    console.log(`   Total streams checked: ${streams.length}`);
    console.log(`   Streams updated: ${fixedCount}`);
    console.log(`   Streams unchanged: ${streams.length - fixedCount}`);

    if (fixedCount > 0) {
      console.log('\n🎉 Platform icons have been fixed! The icons should now display correctly in the streaming status.');
    } else {
      console.log('\n✨ All platform icons were already correct!');
    }

  } catch (error) {
    console.error('❌ Error fixing platform icons:', error);
  }
}

fixPlatformIcons().then(() => {
  console.log('\n🔧 Platform icon fix script completed!');
  process.exit(0);
}).catch(error => {
  console.error('❌ Platform icon fix script failed:', error);
  process.exit(1);
});
