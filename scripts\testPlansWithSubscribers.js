const Subscription = require('../models/Subscription');

async function testPlansWithSubscribers() {
  console.log('🔍 Testing getAllPlansWithSubscribers method...\n');

  try {
    // Test the new method
    const plansWithSubscribers = await Subscription.getAllPlansWithSubscribers();

    console.log('📋 Plans with subscriber counts:');
    plansWithSubscribers.forEach(plan => {
      console.log(`- ${plan.name}: ${plan.subscriber_count} subscribers (${plan.currency} ${plan.price})`);
    });

    // Test the old method for comparison
    const plansWithoutSubscribers = await Subscription.getAllPlans();

    console.log('\n📋 Plans without subscriber counts (old method):');
    plansWithoutSubscribers.forEach(plan => {
      console.log(`- ${plan.name}: ${plan.subscriber_count || 'undefined'} subscribers (${plan.currency} ${plan.price})`);
    });

    console.log('\n✅ Test completed successfully!');
    console.log('📝 The new method correctly includes subscriber counts');
    console.log('📝 Admin panel will now show accurate subscriber counts in both dashboard and plans page');

  } catch (error) {
    console.error('❌ Error testing plans with subscribers:', error);
  }

  process.exit(0);
}

testPlansWithSubscribers();
