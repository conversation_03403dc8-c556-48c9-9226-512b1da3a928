const { db } = require('../db/database');

async function checkPlansData() {
  console.log('🔍 Checking subscription plans data...\n');

  try {
    // Get all plans
    const plans = await new Promise((resolve, reject) => {
      db.all('SELECT * FROM subscription_plans ORDER BY price ASC', [], (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });

    console.log(`📋 Found ${plans.length} subscription plans:\n`);

    plans.forEach((plan, index) => {
      let currencySymbol = '$';
      if (plan.currency === 'IDR') currencySymbol = 'Rp';
      else if (plan.currency === 'EUR') currencySymbol = '€';
      else if (plan.currency === 'GBP') currencySymbol = '£';
      
      console.log(`${index + 1}. Plan: "${plan.name}"`);
      console.log(`   Price: ${currencySymbol}${plan.price}`);
      console.log(`   Currency: ${plan.currency || 'USD'}`);
      console.log(`   Billing: ${plan.billing_period}`);
      console.log(`   Slots: ${plan.max_streaming_slots === -1 ? 'Unlimited' : plan.max_streaming_slots}`);
      console.log(`   Storage: ${plan.max_storage_gb < 1 ? Math.round(plan.max_storage_gb * 1024) + 'MB' : plan.max_storage_gb + 'GB'}`);
      console.log(`   Active: ${plan.is_active ? 'Yes' : 'No'}`);
      console.log('');
    });

    console.log('✅ Plans data check completed!');
  } catch (error) {
    console.error('❌ Error checking plans data:', error);
  }

  process.exit(0);
}

checkPlansData();
