const { db } = require('../db/database');

async function testNewStats() {
  console.log('🔍 Testing new statistics query...\n');

  try {
    // Test the new statistics query
    const newStats = await new Promise((resolve, reject) => {
      db.all(`
        SELECT
          (SELECT COUNT(*) FROM users) as total_users,
          (SELECT COUNT(*) FROM users WHERE is_active = 1) as active_users,
          (SELECT COUNT(*) FROM streams) as total_streams,
          (SELECT COUNT(*) FROM streams WHERE status = 'live') as live_streams,
          (SELECT COUNT(*) FROM videos) as total_videos,
          (SELECT SUM(file_size) FROM videos) as total_storage_bytes,
          (SELECT COUNT(*) FROM users WHERE plan_type != 'Preview' AND plan_type IS NOT NULL) as active_subscriptions
      `, [], (err, rows) => {
        if (err) reject(err);
        else resolve(rows[0]);
      });
    });

    console.log('📊 NEW Statistics Query Results:');
    console.log('- Total users:', newStats.total_users);
    console.log('- Active users:', newStats.active_users);
    console.log('- Total streams:', newStats.total_streams);
    console.log('- Live streams:', newStats.live_streams);
    console.log('- Total videos:', newStats.total_videos);
    console.log('- Active subscriptions (NEW):', newStats.active_subscriptions);

    // Test the old statistics query for comparison
    const oldStats = await new Promise((resolve, reject) => {
      db.all(`
        SELECT
          (SELECT COUNT(*) FROM user_subscriptions WHERE status = 'active') as old_active_subscriptions
      `, [], (err, rows) => {
        if (err) reject(err);
        else resolve(rows[0]);
      });
    });

    console.log('\n📊 OLD vs NEW Comparison:');
    console.log('- OLD active subscriptions (user_subscriptions table):', oldStats.old_active_subscriptions);
    console.log('- NEW active subscriptions (users.plan_type != Preview):', newStats.active_subscriptions);

    // Show breakdown by plan type
    const planBreakdown = await new Promise((resolve, reject) => {
      db.all(`
        SELECT 
          plan_type,
          COUNT(*) as user_count
        FROM users 
        WHERE plan_type IS NOT NULL
        GROUP BY plan_type
        ORDER BY user_count DESC
      `, [], (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });

    console.log('\n📋 Plan Type Breakdown:');
    planBreakdown.forEach(plan => {
      const isSubscriber = plan.plan_type !== 'Preview';
      console.log(`- ${plan.plan_type}: ${plan.user_count} users ${isSubscriber ? '(PAID)' : '(FREE)'}`);
    });

    console.log('\n✅ Statistics test completed!');
  } catch (error) {
    console.error('❌ Error testing statistics:', error);
  }

  process.exit(0);
}

testNewStats();
