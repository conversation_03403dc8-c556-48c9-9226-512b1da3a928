// Debug storage usage calculation
console.log('🔍 Debugging Storage Usage...\n');

const { db } = require('../db/database');
const User = require('../models/User');
const Subscription = require('../models/Subscription');

async function debugStorage() {
  try {
    console.log('1. Checking all users and their storage...');
    
    const users = await new Promise((resolve, reject) => {
      db.all(
        'SELECT id, username, plan_type, max_storage_gb, used_storage_gb FROM users ORDER BY username',
        [],
        (err, rows) => {
          if (err) reject(err);
          else resolve(rows);
        }
      );
    });

    console.log(`Found ${users.length} users:`);
    for (const user of users) {
      console.log(`\n👤 ${user.username} (${user.plan_type}):`);
      console.log(`   Max storage: ${user.max_storage_gb}GB`);
      console.log(`   Used storage: ${user.used_storage_gb || 0}GB`);

      // Get videos for this user
      const videos = await new Promise((resolve, reject) => {
        db.all(
          'SELECT id, title, file_size, created_at FROM videos WHERE user_id = ? ORDER BY created_at DESC',
          [user.id],
          (err, rows) => {
            if (err) reject(err);
            else resolve(rows);
          }
        );
      });

      console.log(`   Videos: ${videos.length}`);
      
      let totalBytes = 0;
      videos.forEach((video, index) => {
        const sizeGB = (video.file_size || 0) / (1024 * 1024 * 1024);
        totalBytes += video.file_size || 0;
        console.log(`     ${index + 1}. ${video.title}: ${sizeGB.toFixed(3)}GB (${video.file_size || 0} bytes)`);
      });

      const calculatedGB = totalBytes / (1024 * 1024 * 1024);
      console.log(`   📊 Calculated total: ${calculatedGB.toFixed(3)}GB`);
      
      const difference = Math.abs(calculatedGB - (user.used_storage_gb || 0));
      if (difference > 0.001) { // More than 1MB difference
        console.log(`   ⚠️  MISMATCH! DB shows ${user.used_storage_gb || 0}GB, calculated ${calculatedGB.toFixed(3)}GB`);
        
        // Update the user's storage usage
        console.log(`   🔧 Fixing storage usage...`);
        await new Promise((resolve, reject) => {
          db.run(
            'UPDATE users SET used_storage_gb = ? WHERE id = ?',
            [calculatedGB, user.id],
            function(err) {
              if (err) {
                console.log(`   ❌ Failed to update: ${err.message}`);
                reject(err);
              } else {
                console.log(`   ✅ Updated storage: ${user.used_storage_gb || 0}GB → ${calculatedGB.toFixed(3)}GB`);
                resolve();
              }
            }
          );
        });
      } else {
        console.log(`   ✅ Storage usage is correct`);
      }
    }

    console.log('\n2. Testing storage update function...');
    
    // Test the updateStorageUsage function
    const testUser = users.find(u => u.username !== 'admin');
    if (testUser) {
      console.log(`\nTesting with user: ${testUser.username}`);
      
      const beforeUpdate = await User.findById(testUser.id);
      console.log(`Before: ${beforeUpdate.used_storage_gb || 0}GB`);
      
      // Add 0.001GB (1MB) as test
      try {
        await Subscription.updateStorageUsage(testUser.id, 0.001);
        console.log('✅ updateStorageUsage function works');
        
        const afterUpdate = await User.findById(testUser.id);
        console.log(`After: ${afterUpdate.used_storage_gb || 0}GB`);
        
        // Revert the test change
        await Subscription.updateStorageUsage(testUser.id, -0.001);
        console.log('✅ Reverted test change');
        
      } catch (error) {
        console.log(`❌ updateStorageUsage failed: ${error.message}`);
      }
    }

    console.log('\n3. Checking video upload middleware...');
    
    // Check if there are any recent videos without proper storage update
    const recentVideos = await new Promise((resolve, reject) => {
      db.all(
        `SELECT v.*, u.username 
         FROM videos v 
         JOIN users u ON v.user_id = u.id 
         ORDER BY v.created_at DESC 
         LIMIT 10`,
        [],
        (err, rows) => {
          if (err) reject(err);
          else resolve(rows);
        }
      );
    });

    console.log(`\nRecent videos (last 10):`);
    recentVideos.forEach((video, index) => {
      const sizeGB = (video.file_size || 0) / (1024 * 1024 * 1024);
      console.log(`${index + 1}. ${video.title} by ${video.username}: ${sizeGB.toFixed(3)}GB`);
    });

    console.log('\n4. Final verification...');
    
    // Re-check all users after fixes
    const updatedUsers = await new Promise((resolve, reject) => {
      db.all(
        'SELECT username, plan_type, max_storage_gb, used_storage_gb FROM users ORDER BY username',
        [],
        (err, rows) => {
          if (err) reject(err);
          else resolve(rows);
        }
      );
    });

    console.log('\nFinal storage status:');
    updatedUsers.forEach(user => {
      const percentage = user.max_storage_gb > 0 ? ((user.used_storage_gb || 0) / user.max_storage_gb * 100).toFixed(1) : 0;
      console.log(`${user.username}: ${user.used_storage_gb || 0}GB / ${user.max_storage_gb}GB (${percentage}%)`);
    });

    console.log('\n✅ Storage debug completed!');

  } catch (error) {
    console.error('❌ Error during storage debug:', error);
  }
}

// Run the debug
debugStorage().then(() => {
  console.log('\nDebug completed');
  process.exit(0);
}).catch(error => {
  console.error('Debug failed:', error);
  process.exit(1);
});
