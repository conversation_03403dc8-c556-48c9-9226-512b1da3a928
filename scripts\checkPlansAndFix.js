// Check plans and fix cancellation bug
console.log('🔍 Checking Plans and Fixing Cancellation Bug...\n');

const { db } = require('../db/database');
const Subscription = require('../models/Subscription');

async function checkPlansAndFix() {
  try {
    console.log('1. Checking existing plans...');
    
    const plans = await new Promise((resolve, reject) => {
      db.all('SELECT * FROM subscription_plans ORDER BY name', [], (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });

    console.log('Available plans:');
    plans.forEach(plan => {
      console.log(`   - ${plan.name}: ${plan.max_streaming_slots} slots, ${plan.max_storage_gb}GB, active: ${plan.is_active}`);
    });

    // Find Preview plan
    const previewPlan = plans.find(p => p.name === 'Preview');
    if (!previewPlan) {
      console.log('❌ Preview plan not found!');
      return;
    }

    console.log(`\n✅ Preview plan found: ${previewPlan.max_streaming_slots} slots, ${previewPlan.max_storage_gb}GB`);

    console.log('\n2. Checking users with Preview plan...');
    
    const previewUsers = await new Promise((resolve, reject) => {
      db.all('SELECT username, plan_type, max_streaming_slots, max_storage_gb FROM users WHERE plan_type = ?', ['Preview'], (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });

    console.log('Preview plan users:');
    previewUsers.forEach(user => {
      const isCorrect = user.max_streaming_slots === previewPlan.max_streaming_slots && 
                       user.max_storage_gb === previewPlan.max_storage_gb;
      const status = isCorrect ? '✅' : '❌';
      console.log(`   ${status} ${user.username}: ${user.max_streaming_slots} slots, ${user.max_storage_gb}GB`);
    });

    console.log('\n3. Checking for users with incorrect limits...');
    
    const incorrectUsers = previewUsers.filter(user => 
      user.max_streaming_slots !== previewPlan.max_streaming_slots || 
      user.max_storage_gb !== previewPlan.max_storage_gb
    );

    if (incorrectUsers.length > 0) {
      console.log(`Found ${incorrectUsers.length} users with incorrect limits:`);
      
      for (const user of incorrectUsers) {
        console.log(`   Fixing ${user.username}: ${user.max_streaming_slots} → ${previewPlan.max_streaming_slots} slots, ${user.max_storage_gb} → ${previewPlan.max_storage_gb}GB`);
        
        await new Promise((resolve, reject) => {
          db.run(
            'UPDATE users SET max_streaming_slots = ?, max_storage_gb = ? WHERE username = ?',
            [previewPlan.max_streaming_slots, previewPlan.max_storage_gb, user.username],
            (err) => {
              if (err) reject(err);
              else resolve();
            }
          );
        });
        
        console.log(`   ✅ Fixed ${user.username}`);
      }
    } else {
      console.log('✅ All Preview plan users have correct limits');
    }

    console.log('\n4. Checking if "free" plan exists...');
    
    const freePlan = plans.find(p => p.name.toLowerCase() === 'free');
    if (freePlan) {
      console.log(`✅ Free plan found: ${freePlan.max_streaming_slots} slots, ${freePlan.max_storage_gb}GB`);
    } else {
      console.log('❌ Free plan not found - this is the source of the bug!');
      console.log('   The cancellation code tries to downgrade to "free" plan but it doesn\'t exist');
      console.log('   So it falls back to hardcoded values: 1 slot, 5GB');
    }

    console.log('\n✅ Analysis completed!');
    console.log('\n📋 Issues found:');
    console.log('   1. Preview plan users should NOT be able to cancel their subscription');
    console.log('   2. Cancellation code tries to downgrade to non-existent "free" plan');
    console.log('   3. Fallback hardcoded values (1 slot, 5GB) are incorrect for Preview plan');

  } catch (error) {
    console.error('❌ Error during analysis:', error);
  }
}

// Run the analysis
checkPlansAndFix().then(() => {
  console.log('\nAnalysis completed');
  process.exit(0);
}).catch(error => {
  console.error('Analysis failed:', error);
  process.exit(1);
});
