const { db } = require('../db/database');

async function simplePreviewCheck() {
  console.log('🔍 Simple Preview Plan Check...\n');
  
  try {
    // Check Preview plan
    const previewPlan = await new Promise((resolve, reject) => {
      db.get('SELECT * FROM subscription_plans WHERE name = "Preview"', [], (err, row) => {
        if (err) reject(err);
        else resolve(row);
      });
    });

    console.log('Preview Plan Configuration:');
    if (previewPlan) {
      console.log(`  - Streaming slots: ${previewPlan.max_streaming_slots}`);
      console.log(`  - Storage: ${previewPlan.max_storage_gb}GB`);
      console.log(`  - Active: ${previewPlan.is_active}`);
    } else {
      console.log('  ❌ Preview plan not found!');
      return;
    }

    // Check Preview users
    const previewUsers = await new Promise((resolve, reject) => {
      db.all(`
        SELECT username, max_streaming_slots, max_storage_gb,
               (SELECT COUNT(*) FROM streams WHERE user_id = users.id) as stream_count
        FROM users 
        WHERE plan_type = "Preview"
      `, [], (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });

    console.log('\nPreview Plan Users:');
    if (previewUsers.length > 0) {
      previewUsers.forEach(user => {
        console.log(`  - ${user.username}: ${user.stream_count} streams, ${user.max_streaming_slots} slots allowed, ${user.max_storage_gb}GB`);
      });
    } else {
      console.log('  No Preview users found');
    }

    // Fix if needed
    if (previewPlan.max_streaming_slots !== 0) {
      console.log('\n🔧 Fixing Preview plan to 0 slots...');
      await new Promise((resolve, reject) => {
        db.run('UPDATE subscription_plans SET max_streaming_slots = 0 WHERE name = "Preview"', [], (err) => {
          if (err) reject(err);
          else resolve();
        });
      });
      console.log('✅ Preview plan fixed to 0 slots');
    }

    // Fix users
    for (const user of previewUsers) {
      if (user.max_streaming_slots !== 0) {
        console.log(`🔧 Fixing user ${user.username} to 0 slots...`);
        await new Promise((resolve, reject) => {
          db.run('UPDATE users SET max_streaming_slots = 0 WHERE username = ?', [user.username], (err) => {
            if (err) reject(err);
            else resolve();
          });
        });
        console.log(`✅ Fixed user ${user.username}`);
      }
    }

    console.log('\n✅ Check completed!');

  } catch (error) {
    console.error('❌ Error:', error);
  }
  
  process.exit(0);
}

simplePreviewCheck();
