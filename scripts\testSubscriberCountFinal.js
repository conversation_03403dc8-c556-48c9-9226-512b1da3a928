const { db } = require('../db/database');

async function testSubscriberCountFinal() {
  console.log('🔍 Final test: Verifying subscriber counts in admin interface...\n');

  try {
    // Show current state
    console.log('📊 Current State:');
    const currentStats = await new Promise((resolve, reject) => {
      db.all(`
        SELECT 
          sp.name,
          sp.price,
          sp.currency,
          COUNT(u.id) as subscriber_count
        FROM subscription_plans sp
        LEFT JOIN users u ON u.plan_type = sp.name
        WHERE sp.is_active = 1
        GROUP BY sp.id
        ORDER BY sp.price ASC
      `, [], (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });

    currentStats.forEach(plan => {
      console.log(`- ${plan.name}: ${plan.subscriber_count} subscribers (${plan.currency} ${plan.price})`);
    });

    // Temporarily update one user to Basic plan for testing
    console.log('\n🧪 Testing: Temporarily updating one user to Basic plan...');
    
    await new Promise((resolve, reject) => {
      db.run(
        "UPDATE users SET plan_type = 'Basic' WHERE username = 'aufanirsad'",
        [],
        function(err) {
          if (err) reject(err);
          else resolve();
        }
      );
    });

    // Check the counts again
    const testStats = await new Promise((resolve, reject) => {
      db.all(`
        SELECT 
          sp.name,
          sp.price,
          sp.currency,
          COUNT(u.id) as subscriber_count
        FROM subscription_plans sp
        LEFT JOIN users u ON u.plan_type = sp.name
        WHERE sp.is_active = 1
        GROUP BY sp.id
        ORDER BY sp.price ASC
      `, [], (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });

    console.log('\n📊 After Test Update:');
    testStats.forEach(plan => {
      console.log(`- ${plan.name}: ${plan.subscriber_count} subscribers (${plan.currency} ${plan.price})`);
    });

    // Test the admin stats query
    const adminStats = await new Promise((resolve, reject) => {
      db.all(`
        SELECT
          (SELECT COUNT(*) FROM users WHERE plan_type != 'Preview' AND plan_type IS NOT NULL) as active_subscriptions
      `, [], (err, rows) => {
        if (err) reject(err);
        else resolve(rows[0]);
      });
    });

    console.log('\n📊 Admin Stats Query Result:');
    console.log(`- Active subscriptions: ${adminStats.active_subscriptions}`);

    console.log('\n✅ Test Results:');
    console.log('- Preview plan should show 4 subscribers (down from 5)');
    console.log('- Basic plan should show 1 subscriber (up from 0)');
    console.log('- Active subscriptions should be 1');
    console.log('- Admin dashboard cards should show these counts');
    console.log('- Admin plans table should show these counts');

    console.log('\n🔄 Restoring user back to Preview plan...');
    await new Promise((resolve, reject) => {
      db.run(
        "UPDATE users SET plan_type = 'Preview' WHERE username = 'aufanirsad'",
        [],
        function(err) {
          if (err) reject(err);
          else resolve();
        }
      );
    });

    console.log('✅ User restored to Preview plan');
    console.log('\n🎉 All tests completed successfully!');
    console.log('📝 The admin panel should now show correct subscriber counts in both:');
    console.log('   - Dashboard subscription plans cards');
    console.log('   - Plans management table');

  } catch (error) {
    console.error('❌ Error in final test:', error);
  }

  process.exit(0);
}

testSubscriberCountFinal();
