const { db } = require('../db/database');
const Stream = require('../models/Stream');
const Subscription = require('../models/Subscription');
const User = require('../models/User');

async function testStreamDeletionOnPreviewDowngrade() {
  console.log('🚀 Testing Stream Deletion on Preview Plan Downgrade...\n');
  
  try {
    // Find a user with streams (preferably Basic plan user)
    const usersWithStreams = await new Promise((resolve, reject) => {
      db.all(`
        SELECT u.*, COUNT(s.id) as stream_count
        FROM users u
        LEFT JOIN streams s ON u.id = s.user_id
        WHERE u.plan_type != 'Preview'
        GROUP BY u.id
        HAVING stream_count > 0
        LIMIT 1
      `, [], (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });

    if (usersWithStreams.length === 0) {
      console.log('❌ No users with streams found for testing');
      
      // Create a test user with streams
      console.log('📝 Creating test scenario...');
      
      // Find a Basic plan user
      const basicUsers = await new Promise((resolve, reject) => {
        db.all('SELECT * FROM users WHERE plan_type = "Basic" LIMIT 1', [], (err, rows) => {
          if (err) reject(err);
          else resolve(rows);
        });
      });

      if (basicUsers.length === 0) {
        console.log('❌ No Basic plan users found');
        return;
      }

      const testUser = basicUsers[0];
      console.log(`📝 Using test user: ${testUser.username} (${testUser.plan_type})`);

      // Create test streams
      const testStreams = [];
      for (let i = 1; i <= 2; i++) {
        const stream = await Stream.create({
          user_id: testUser.id,
          title: `Test Stream ${i}`,
          description: `Test stream for deletion testing ${i}`,
          stream_key: `test-key-${i}-${Date.now()}`,
          status: 'offline'
        });
        testStreams.push(stream);
        console.log(`   ✅ Created test stream: ${stream.title}`);
      }

      // Test the deletion function
      console.log('\n🗑️ Testing stream deletion function...');
      const deletionResult = await Subscription.deleteAllUserStreamsForPreviewPlan(testUser.id);
      
      console.log(`📊 Deletion result:`);
      console.log(`   - Streams deleted: ${deletionResult.deleted}`);
      console.log(`   - Success: ${deletionResult.success}`);
      
      if (deletionResult.streams && deletionResult.streams.length > 0) {
        console.log(`   - Deleted streams:`);
        deletionResult.streams.forEach(stream => {
          console.log(`     • ${stream.title} (${stream.id})`);
        });
      }

      // Verify deletion
      const remainingStreams = await new Promise((resolve, reject) => {
        db.all('SELECT * FROM streams WHERE user_id = ?', [testUser.id], (err, rows) => {
          if (err) reject(err);
          else resolve(rows);
        });
      });

      console.log(`\n✅ Verification: ${remainingStreams.length} streams remaining (should be 0)`);
      
      if (remainingStreams.length === 0) {
        console.log('🎉 Stream deletion test PASSED!');
      } else {
        console.log('❌ Stream deletion test FAILED!');
        remainingStreams.forEach(stream => {
          console.log(`   - Remaining: ${stream.title} (${stream.id})`);
        });
      }

      return;
    }

    const testUser = usersWithStreams[0];
    console.log(`📊 Testing with user: ${testUser.username}`);
    console.log(`   Plan: ${testUser.plan_type}`);
    console.log(`   Streams: ${testUser.stream_count}`);

    // Get detailed stream information
    const userStreams = await new Promise((resolve, reject) => {
      db.all('SELECT id, title, status FROM streams WHERE user_id = ?', [testUser.id], (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });

    console.log('\n📋 Current streams:');
    userStreams.forEach(stream => {
      console.log(`   - ${stream.title} (${stream.id}) - Status: ${stream.status}`);
    });

    // Test the deletion function
    console.log('\n🗑️ Testing stream deletion function...');
    const deletionResult = await Subscription.deleteAllUserStreamsForPreviewPlan(testUser.id);
    
    console.log(`📊 Deletion result:`);
    console.log(`   - Streams deleted: ${deletionResult.deleted}`);
    console.log(`   - Success: ${deletionResult.success}`);
    
    if (deletionResult.streams && deletionResult.streams.length > 0) {
      console.log(`   - Deleted streams:`);
      deletionResult.streams.forEach(stream => {
        console.log(`     • ${stream.title} (${stream.id})`);
      });
    }

    // Verify deletion
    const remainingStreams = await new Promise((resolve, reject) => {
      db.all('SELECT * FROM streams WHERE user_id = ?', [testUser.id], (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });

    console.log(`\n✅ Verification: ${remainingStreams.length} streams remaining (should be 0)`);
    
    if (remainingStreams.length === 0) {
      console.log('🎉 Stream deletion test PASSED!');
    } else {
      console.log('❌ Stream deletion test FAILED!');
      remainingStreams.forEach(stream => {
        console.log(`   - Remaining: ${stream.title} (${stream.id})`);
      });
    }

    // Test quota check after deletion
    console.log('\n📊 Testing quota check after deletion...');
    const quotaCheck = await Subscription.checkStreamingSlotLimit(testUser.id);
    console.log(`   Current slots: ${quotaCheck.currentSlots}/${quotaCheck.maxSlots}`);
    console.log(`   Has limit: ${quotaCheck.hasLimit}`);

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

async function testFullCancellationFlow() {
  console.log('\n🔄 Testing Full Cancellation Flow...\n');
  
  try {
    // Find a Basic plan user with streams
    const basicUsers = await new Promise((resolve, reject) => {
      db.all(`
        SELECT u.*, COUNT(s.id) as stream_count
        FROM users u
        LEFT JOIN streams s ON u.id = s.user_id
        WHERE u.plan_type = 'Basic'
        GROUP BY u.id
        HAVING stream_count >= 0
        LIMIT 1
      `, [], (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });

    if (basicUsers.length === 0) {
      console.log('❌ No Basic plan users found for testing');
      return;
    }

    const testUser = basicUsers[0];
    console.log(`📊 Testing cancellation flow with user: ${testUser.username}`);
    console.log(`   Current plan: ${testUser.plan_type}`);
    console.log(`   Current streams: ${testUser.stream_count}`);

    // Create test streams if user has none
    if (testUser.stream_count === 0) {
      console.log('📝 Creating test streams...');
      for (let i = 1; i <= 2; i++) {
        const stream = await Stream.create({
          user_id: testUser.id,
          title: `Test Stream ${i}`,
          description: `Test stream for cancellation testing ${i}`,
          stream_key: `test-cancel-key-${i}-${Date.now()}`,
          status: 'offline'
        });
        console.log(`   ✅ Created test stream: ${stream.title}`);
      }
    }

    // Check streams before cancellation
    const streamsBefore = await new Promise((resolve, reject) => {
      db.all('SELECT COUNT(*) as count FROM streams WHERE user_id = ?', [testUser.id], (err, rows) => {
        if (err) reject(err);
        else resolve(rows[0].count);
      });
    });

    console.log(`\n📊 Before cancellation: ${streamsBefore} streams`);

    // Simulate the cancellation process (without actually cancelling subscription)
    console.log('\n🗑️ Simulating stream deletion for Preview plan downgrade...');
    const deletionResult = await Subscription.deleteAllUserStreamsForPreviewPlan(testUser.id);

    // Check streams after deletion
    const streamsAfter = await new Promise((resolve, reject) => {
      db.all('SELECT COUNT(*) as count FROM streams WHERE user_id = ?', [testUser.id], (err, rows) => {
        if (err) reject(err);
        else resolve(rows[0].count);
      });
    });

    console.log(`\n📊 After deletion: ${streamsAfter} streams`);
    console.log(`📊 Deleted: ${deletionResult.deleted} streams`);

    if (streamsAfter === 0 && deletionResult.deleted === streamsBefore) {
      console.log('🎉 Full cancellation flow test PASSED!');
      console.log('✅ All streams successfully deleted for Preview plan downgrade');
    } else {
      console.log('❌ Full cancellation flow test FAILED!');
      console.log(`   Expected: 0 streams remaining, ${streamsBefore} deleted`);
      console.log(`   Actual: ${streamsAfter} streams remaining, ${deletionResult.deleted} deleted`);
    }

  } catch (error) {
    console.error('❌ Full cancellation flow test failed:', error);
  }
}

async function main() {
  console.log('🚀 Testing Preview Plan Stream Deletion Functionality...\n');
  
  await testStreamDeletionOnPreviewDowngrade();
  await testFullCancellationFlow();
  
  console.log('\n🎉 All tests completed!');
  process.exit(0);
}

main().catch(error => {
  console.error('❌ Test script failed:', error);
  process.exit(1);
});
